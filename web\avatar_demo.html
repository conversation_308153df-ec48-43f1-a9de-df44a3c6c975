<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字人形象生成演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .demo-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        .form-control {
            border-radius: 10px;
            border: 1px solid #ddd;
            padding: 12px 15px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
        }
        .progress {
            height: 25px;
            border-radius: 15px;
        }
        .progress-bar {
            border-radius: 15px;
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list i {
            color: #667eea;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="card">
            <div class="card-header text-center">
                <h1><i class="bi bi-person-plus"></i> 数字人形象生成演示</h1>
                <p class="mb-0">上传视频，生成专属数字人形象</p>
            </div>
            <div class="card-body p-4">
                <!-- 功能介绍 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5><i class="bi bi-info-circle"></i> 功能特点</h5>
                        <ul class="feature-list">
                            <li><i class="bi bi-check-circle"></i> 支持多种视频格式</li>
                            <li><i class="bi bi-check-circle"></i> 智能人脸检测</li>
                            <li><i class="bi bi-check-circle"></i> 可调节参数</li>
                            <li><i class="bi bi-check-circle"></i> 高质量输出</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="bi bi-exclamation-triangle"></i> 注意事项</h5>
                        <ul class="feature-list">
                            <li><i class="bi bi-camera-video"></i> 视频需包含清晰人脸</li>
                            <li><i class="bi bi-clock"></i> 建议时长5-30秒</li>
                            <li><i class="bi bi-brightness-high"></i> 光照条件良好</li>
                            <li><i class="bi bi-cpu"></i> 需要GPU加速</li>
                        </ul>
                    </div>
                </div>

                <!-- 生成表单 -->
                <form id="avatar-form" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="avatar-video" class="form-label">
                                <i class="bi bi-camera-video"></i> 选择视频文件
                            </label>
                            <input type="file" class="form-control" id="avatar-video" accept="video/*" required>
                            <div class="form-text">支持MP4、AVI、MOV等格式</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="avatar-id" class="form-label">
                                <i class="bi bi-person-badge"></i> 数字人ID
                            </label>
                            <input type="text" class="form-control" id="avatar-id" placeholder="my_avatar_001" required>
                            <div class="form-text">只能包含字母、数字和下划线</div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="img-size" class="form-label">
                                <i class="bi bi-aspect-ratio"></i> 图像尺寸: <span id="img-size-value">96</span>px
                            </label>
                            <input type="range" class="form-range" id="img-size" min="64" max="256" value="96" step="32">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="batch-size" class="form-label">
                                <i class="bi bi-layers"></i> 批处理大小: <span id="batch-size-value">16</span>
                            </label>
                            <input type="range" class="form-range" id="batch-size" min="1" max="32" value="16">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label"><i class="bi bi-border-all"></i> 边距设置</label>
                        <div class="row">
                            <div class="col-3">
                                <input type="number" class="form-control" id="pad-top" value="0" min="0" max="50">
                                <small class="text-muted">上</small>
                            </div>
                            <div class="col-3">
                                <input type="number" class="form-control" id="pad-bottom" value="10" min="0" max="50">
                                <small class="text-muted">下</small>
                            </div>
                            <div class="col-3">
                                <input type="number" class="form-control" id="pad-left" value="0" min="0" max="50">
                                <small class="text-muted">左</small>
                            </div>
                            <div class="col-3">
                                <input type="number" class="form-control" id="pad-right" value="0" min="0" max="50">
                                <small class="text-muted">右</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="no-smooth">
                            <label class="form-check-label" for="no-smooth">
                                <i class="bi bi-toggle-off"></i> 禁用面部检测平滑
                            </label>
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg" id="generate-btn">
                            <i class="bi bi-play-circle"></i> 开始生成数字人形象
                        </button>
                    </div>
                </form>

                <!-- 进度显示 -->
                <div id="progress-section" class="mt-4" style="display: none;">
                    <h5><i class="bi bi-hourglass-split"></i> 生成进度</h5>
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%"></div>
                    </div>
                    <div class="text-center mt-2">
                        <small id="progress-text">准备中...</small>
                    </div>
                </div>

                <!-- 结果显示 -->
                <div id="result-section" class="mt-4" style="display: none;">
                    <div class="alert alert-success">
                        <h5><i class="bi bi-check-circle"></i> 生成成功！</h5>
                        <p>数字人形象已生成完成</p>
                        <p><strong>保存位置:</strong> <span id="avatar-path"></span></p>
                        <hr>
                        <p class="mb-0">
                            <strong>下一步:</strong> 重启服务时使用新的avatar_id：<br>
                            <code>python app.py --transport webrtc --model wav2lip --avatar_id <span id="new-avatar-id"></span></code>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <a href="dashboard.html" class="btn btn-outline-primary">
                <i class="bi bi-arrow-left"></i> 返回主界面
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // 滑块更新
            $('#img-size').on('input', function() {
                $('#img-size-value').text($(this).val());
            });

            $('#batch-size').on('input', function() {
                $('#batch-size-value').text($(this).val());
            });

            // 表单提交
            $('#avatar-form').on('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData();
                const videoFile = $('#avatar-video')[0].files[0];
                const avatarId = $('#avatar-id').val().trim();
                
                // 验证
                if (!videoFile) {
                    alert('请选择视频文件');
                    return;
                }
                
                if (!avatarId || !/^[a-zA-Z0-9_]+$/.test(avatarId)) {
                    alert('请输入有效的数字人ID（只能包含字母、数字和下划线）');
                    return;
                }
                
                // 收集参数
                formData.append('video', videoFile);
                formData.append('avatar_id', avatarId);
                formData.append('img_size', $('#img-size').val());
                formData.append('pad_top', $('#pad-top').val());
                formData.append('pad_bottom', $('#pad-bottom').val());
                formData.append('pad_left', $('#pad-left').val());
                formData.append('pad_right', $('#pad-right').val());
                formData.append('nosmooth', $('#no-smooth').is(':checked'));
                formData.append('face_det_batch_size', $('#batch-size').val());
                
                // 显示进度
                $('#progress-section').show();
                $('#result-section').hide();
                $('#generate-btn').prop('disabled', true);
                updateProgress(0, '开始上传文件...');
                
                // 发送请求
                fetch('/generate_avatar', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        updateProgress(100, '生成完成！');
                        setTimeout(() => {
                            $('#progress-section').hide();
                            $('#result-section').show();
                            $('#avatar-path').text(data.avatar_path);
                            $('#new-avatar-id').text(avatarId);
                            $('#generate-btn').prop('disabled', false);
                        }, 1000);
                    } else {
                        throw new Error(data.msg || '生成失败');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('生成失败: ' + error.message);
                    $('#progress-section').hide();
                    $('#generate-btn').prop('disabled', false);
                });
            });
            
            function updateProgress(percent, text) {
                $('#progress-section .progress-bar').css('width', percent + '%');
                $('#progress-text').text(text);
            }
        });
    </script>
</body>
</html>
