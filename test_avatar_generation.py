#!/usr/bin/env python3
"""
测试数字人形象生成功能
"""

import requests
import os
import sys

def test_avatar_generation():
    """测试数字人生成API"""
    
    # 服务器地址
    server_url = "http://localhost:8010"
    
    # 检查服务是否运行
    try:
        response = requests.get(f"{server_url}/dashboard.html", timeout=5)
        if response.status_code != 200:
            print("❌ 服务器未运行或无法访问")
            return False
    except requests.exceptions.RequestException:
        print("❌ 无法连接到服务器，请确保服务已启动")
        print("启动命令: python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar1")
        return False
    
    print("✅ 服务器连接正常")
    
    # 检查是否有测试视频文件
    test_video_path = "test_video.mp4"
    if not os.path.exists(test_video_path):
        print(f"❌ 测试视频文件 {test_video_path} 不存在")
        print("请准备一个包含人脸的测试视频文件")
        return False
    
    print(f"✅ 找到测试视频文件: {test_video_path}")
    
    # 准备测试数据
    test_avatar_id = "test_avatar_001"
    
    # 准备文件和数据
    files = {
        'video': open(test_video_path, 'rb')
    }
    
    data = {
        'avatar_id': test_avatar_id,
        'img_size': '96',
        'pad_top': '0',
        'pad_bottom': '10',
        'pad_left': '0',
        'pad_right': '0',
        'nosmooth': 'false',
        'face_det_batch_size': '16'
    }
    
    try:
        print("🚀 开始生成数字人形象...")
        print(f"   数字人ID: {test_avatar_id}")
        print(f"   视频文件: {test_video_path}")
        
        # 发送请求
        response = requests.post(
            f"{server_url}/generate_avatar",
            files=files,
            data=data,
            timeout=300  # 5分钟超时
        )
        
        files['video'].close()
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 0:
                print("✅ 数字人形象生成成功！")
                print(f"   保存路径: {result.get('avatar_path')}")
                
                # 检查生成的文件
                avatar_path = result.get('avatar_path')
                if avatar_path and os.path.exists(avatar_path):
                    print("✅ 生成的文件夹存在")
                    
                    # 检查必要的子文件夹
                    required_dirs = ['full_imgs', 'face_imgs']
                    required_files = ['coords.pkl']
                    
                    for dir_name in required_dirs:
                        dir_path = os.path.join(avatar_path, dir_name)
                        if os.path.exists(dir_path):
                            file_count = len(os.listdir(dir_path))
                            print(f"✅ {dir_name}/ 文件夹存在，包含 {file_count} 个文件")
                        else:
                            print(f"❌ {dir_name}/ 文件夹不存在")
                    
                    for file_name in required_files:
                        file_path = os.path.join(avatar_path, file_name)
                        if os.path.exists(file_path):
                            print(f"✅ {file_name} 文件存在")
                        else:
                            print(f"❌ {file_name} 文件不存在")
                    
                    return True
                else:
                    print(f"❌ 生成的文件夹不存在: {avatar_path}")
                    return False
            else:
                print(f"❌ 生成失败: {result.get('msg')}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时，生成过程可能需要更长时间")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("数字人形象生成功能测试")
    print("=" * 50)
    
    success = test_avatar_generation()
    
    print("=" * 50)
    if success:
        print("🎉 测试通过！数字人形象生成功能正常工作")
        print("\n下一步:")
        print("1. 可以在Web界面中使用生成功能")
        print("2. 重启服务时使用新的avatar_id:")
        print("   python app.py --transport webrtc --model wav2lip --avatar_id test_avatar_001")
    else:
        print("❌ 测试失败，请检查上述错误信息")
        print("\n故障排除:")
        print("1. 确保服务已启动")
        print("2. 确保有测试视频文件")
        print("3. 检查服务器日志")
    print("=" * 50)

if __name__ == "__main__":
    main()
