<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>WebRTC webcam</title>
    <style>
    button {
        padding: 8px 16px;
    }

    video {
        width: 100%;
    }

    .option {
        margin-bottom: 8px;
    }

    #media {
        max-width: 1280px;
    }
    </style>
</head>
<body>

<div class="option">
    <input id="use-stun" type="checkbox"/>
    <label for="use-stun">Use STUN server</label>
</div>
<button id="start" onclick="start()">Start</button>
<button id="stop" style="display: none" onclick="stop()">Stop</button>
<form class="form-inline" id="echo-form">
    <div class="form-group">
      <p>input text</p>

      <textarea cols="2" rows="3" style="width:600px;height:50px;" class="form-control" id="message">test</textarea>
    </div>
    <button type="submit" class="btn btn-default">Send</button>
  </form>

<div id="media">
    <h2>Media</h2>

    <audio id="audio" autoplay="true"></audio>
    <video id="video" style="width:600px;" autoplay="true" playsinline="true"></video>
</div>

<script src="client.js"></script>
<script type="text/javascript" src="http://cdn.sockjs.org/sockjs-0.3.4.js"></script>
<script type="text/javascript" src="https://code.jquery.com/jquery-2.1.1.min.js"></script>
</body>
<script type="text/javascript" charset="utf-8">

	$(document).ready(function() {
	  var host = window.location.hostname
	  var ws = new WebSocket("ws://"+host+":8000/humanecho");
	  //document.getElementsByTagName("video")[0].setAttribute("src", aa["video"]);
	  ws.onopen = function() {
		console.log('Connected');
	  };
	  ws.onmessage = function(e) {
		console.log('Received: ' + e.data);
		data = e
		var vid = JSON.parse(data.data); 
		console.log(typeof(vid),vid)
		//document.getElementsByTagName("video")[0].setAttribute("src", vid["video"]);
		
	  };
	  ws.onclose = function(e) {
		console.log('Closed');
	  };

	  $('#echo-form').on('submit', function(e) {
		e.preventDefault();
		var message = $('#message').val();
		console.log('Sending: ' + message);
		ws.send(message);
		$('#message').val('');
	  });
	});
</script>
</html>
