# 数字人形象生成功能使用指南

## 功能概述

本功能允许用户通过Web界面上传视频文件并生成数字人形象，生成的数字人形象将保存在`data/avatars`文件夹下。

## 使用步骤

### 1. 启动数字人服务
```bash
python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar1
```

### 2. 访问Web界面
在浏览器中打开：`http://localhost:8010/dashboard.html`

### 3. 生成数字人形象
1. 点击"数字人生成"标签页
2. 填写以下参数：
   - **上传视频文件**: 选择包含人脸的视频文件（支持MP4、AVI、MOV等格式）
   - **数字人ID**: 输入唯一的标识符（只能包含字母、数字和下划线）
   - **图像尺寸**: 调整生成图像的尺寸（64-256px，默认96px）
   - **边距设置**: 调整人脸检测的边距（上、下、左、右）
   - **禁用面部检测平滑**: 可选，关闭后可能提高处理速度但降低稳定性
   - **批处理大小**: 调整批处理大小（1-32，默认16）

3. 点击"生成数字人形象"按钮
4. 等待生成完成（会显示进度条）

### 4. 使用生成的数字人
生成完成后，数字人形象将保存在`data/avatars/{数字人ID}`目录下，包含以下文件：
- `full_imgs/`: 完整的图像帧
- `face_imgs/`: 人脸区域图像
- `coords.pkl`: 人脸坐标信息

要使用新生成的数字人，重新启动服务时指定新的avatar_id：
```bash
python app.py --transport webrtc --model wav2lip --avatar_id {你的数字人ID}
```

## 参数说明

### 图像尺寸 (img_size)
- 范围：64-256px
- 默认：96px
- 说明：较大的尺寸会提高质量但增加处理时间和内存使用

### 边距设置 (pads)
- **上边距**: 人脸检测框上方的额外像素
- **下边距**: 人脸检测框下方的额外像素（建议保持10以包含下巴）
- **左边距**: 人脸检测框左侧的额外像素
- **右边距**: 人脸检测框右侧的额外像素

### 批处理大小 (face_det_batch_size)
- 范围：1-32
- 默认：16
- 说明：较大的批处理可能会提高速度但需要更多GPU内存

## 注意事项

1. **视频要求**：
   - 视频中必须包含清晰的人脸
   - 建议时长5-30秒
   - 人脸应该占据画面的合理比例
   - 光照条件良好

2. **系统要求**：
   - 需要GPU支持以获得最佳性能
   - 确保有足够的磁盘空间存储生成的文件

3. **处理时间**：
   - 处理时间取决于视频长度、分辨率和系统性能
   - 通常需要几分钟到十几分钟不等

4. **文件管理**：
   - 生成的数字人文件会占用一定的磁盘空间
   - 可以手动删除不需要的数字人文件夹

## 故障排除

### 常见错误
1. **"Face not detected"**: 视频中没有检测到人脸，请确保视频包含清晰的人脸
2. **"数字人ID格式不正确"**: ID只能包含字母、数字和下划线
3. **内存不足**: 尝试减小批处理大小或图像尺寸

### 日志查看
服务运行时会在控制台输出详细的处理日志，可以根据日志信息排查问题。

## API接口

如果需要通过API直接调用，可以使用以下接口：

```
POST /generate_avatar
Content-Type: multipart/form-data

参数：
- video: 视频文件
- avatar_id: 数字人ID
- img_size: 图像尺寸
- pad_top, pad_bottom, pad_left, pad_right: 边距设置
- nosmooth: 是否禁用平滑
- face_det_batch_size: 批处理大小
```

响应：
```json
{
  "code": 0,
  "msg": "生成成功",
  "avatar_path": "data/avatars/your_avatar_id"
}
```
